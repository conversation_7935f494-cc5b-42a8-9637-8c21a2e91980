@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 基础样式 */
@layer base {
  html {
    font-family: 'Inter', sans-serif;
  }
  
  body {
    @apply bg-gray-900 text-white overflow-x-hidden;
  }
}

/* 霓虹效果组件 */
@layer components {
  .neon-text {
    @apply text-transparent bg-clip-text;
    background-image: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff);
    text-shadow: 0 0 10px rgba(255, 0, 110, 0.5),
                 0 0 20px rgba(131, 56, 236, 0.3),
                 0 0 30px rgba(58, 134, 255, 0.2);
  }
  
  .neon-border {
    border: 1px solid transparent;
    background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
  
  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }
  
  .star-glow {
    filter: drop-shadow(0 0 6px currentColor) drop-shadow(0 0 12px currentColor);
  }
  
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }
}

/* 动画定义 */
@layer utilities {
  @keyframes pulse-glow {
    from {
      filter: drop-shadow(0 0 6px currentColor) drop-shadow(0 0 12px currentColor);
    }
    to {
      filter: drop-shadow(0 0 12px currentColor) drop-shadow(0 0 24px currentColor);
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes twinkle {
    0%, 100% {
      opacity: 0.3;
    }
    50% {
      opacity: 1;
    }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-twinkle {
    animation: twinkle 2s ease-in-out infinite;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gradient-to-b from-purple-500 to-pink-500 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply from-purple-400 to-pink-400;
}
