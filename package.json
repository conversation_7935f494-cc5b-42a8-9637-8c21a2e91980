{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@nuxtjs/supabase": "^1.5.3", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.1", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/typography": "^0.5.16", "@vueuse/nuxt": "^13.5.0", "nuxt": "^3.17.6", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}